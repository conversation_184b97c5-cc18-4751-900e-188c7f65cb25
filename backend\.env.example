# Flash Audit Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Frontend Configuration
SITE_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# OpenRouter API Configuration for Flash Audit
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Dual Model Setup for AI Analysis
# Primary API Key for Kimi Dev 72b (Complex Analysis, Security, DeFi)
OPENROUTER_API_KEY_KIMI=your-kimi-api-key-here
KIMI_MODEL=moonshot/kimi-dev-72b

# Secondary API Key for Google Gemma (Quick Analysis, Code Quality, Gas Optimization)
OPENROUTER_API_KEY_GEMMA=your-gemma-api-key-here
GEMMA_MODEL=google/gemma-2-3b-it

# Model Assignment Strategy
# Kimi Dev 72b: Security analysis, DeFi risks, Cross-chain analysis, MEV detection
# Google Gemma: Code quality, Gas optimization, Quick syntax validation
DEFAULT_MODEL_STRATEGY=dual

# Agent-Specific Model Configuration
SECURITY_AGENT_MODEL=moonshot/kimi-dev-72b
SECURITY_AGENT_API_KEY=KIMI
DEFI_AGENT_MODEL=moonshot/kimi-dev-72b
DEFI_AGENT_API_KEY=KIMI
CROSS_CHAIN_AGENT_MODEL=moonshot/kimi-dev-72b
CROSS_CHAIN_AGENT_API_KEY=KIMI
MEV_AGENT_MODEL=moonshot/kimi-dev-72b
MEV_AGENT_API_KEY=KIMI
ECONOMICS_AGENT_MODEL=moonshot/kimi-dev-72b
ECONOMICS_AGENT_API_KEY=KIMI
GOVERNANCE_AGENT_MODEL=moonshot/kimi-dev-72b
GOVERNANCE_AGENT_API_KEY=KIMI

QUALITY_AGENT_MODEL=google/gemma-2-3b-it
QUALITY_AGENT_API_KEY=GEMMA
GAS_OPTIMIZATION_AGENT_MODEL=google/gemma-2-3b-it
GAS_OPTIMIZATION_AGENT_API_KEY=GEMMA

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Ethereum Testnet URLs (for testing)
SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_MUMBAI_RPC_URL=https://polygon-mumbai.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# Etherscan API Keys for contract verification
ETHERSCAN_API_KEY=your-etherscan-api-key
BSCSCAN_API_KEY=your-bscscan-api-key
POLYGONSCAN_API_KEY=your-polygonscan-api-key

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# TEE Monitor Configuration
TEE_LOG_ENABLED=true
TEE_LOG_PATH=./logs/auditLogs.json
TEE_ENCRYPTION_KEY=your-tee-encryption-key-here

# Audit Configuration
MAX_CONTRACT_SIZE_BYTES=1048576
AUDIT_TIMEOUT_MS=30000
VULNERABILITY_THRESHOLD_HIGH=80
VULNERABILITY_THRESHOLD_MEDIUM=50

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# ChainIDE Integration Configuration
ENABLE_CHAINIDE_INTEGRATION=true
CHAINIDE_WS_PORT=8080
CHAINIDE_MAX_CONNECTIONS=1000
CHAINIDE_ENABLE_COLLABORATION=true
CHAINIDE_ENABLE_REALTIME_ANALYSIS=true
CHAINIDE_ENABLE_CODE_COMPLETION=true
CHAINIDE_DEBOUNCE_DELAY=1000
CHAINIDE_MAX_QUEUE_SIZE=100

# Real-Time Development Features
ENABLE_SYNTAX_VALIDATION=true
ENABLE_LIVE_ANALYSIS=true
ENABLE_CODE_COMPLETION=true
ENABLE_SMART_SUGGESTIONS=true

# Collaborative Features
ENABLE_WORKSPACE_COLLABORATION=true
MAX_WORKSPACE_MEMBERS=10
WORKSPACE_AUTO_SAVE_INTERVAL=30000
ENABLE_REAL_TIME_CURSORS=true
ENABLE_COLLABORATIVE_COMMENTS=true

# Plugin System
ENABLE_PLUGIN_SYSTEM=true
PLUGIN_SDK_VERSION=1.0.0
MAX_CUSTOM_PLUGINS=50