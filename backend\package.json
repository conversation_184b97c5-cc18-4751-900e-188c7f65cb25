{"name": "smart-contract-auditor-backend", "version": "1.0.0", "description": "AI-powered Smart Contract Security Auditor Backend with LLM integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node src/server.js", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "audit:security": "npm audit"}, "keywords": ["smart-contract", "security", "auditor", "blockchain", "web3", "solidity", "vulnerability", "llm", "ai"], "author": "Smart Contract Auditor Team", "license": "MIT", "dependencies": {"@solidity-parser/parser": "^0.20.1", "@supabase/supabase-js": "^2.50.2", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.8.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "node-cron": "^3.0.3", "rate-limiter-flexible": "^7.1.1", "uuid": "^9.0.1", "web3": "^4.2.2", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"chai": "^5.2.0", "eslint": "^8.54.0", "jest": "^29.7.0", "mocha": "^11.7.0", "nyc": "^17.1.0", "sinon": "^21.0.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/smart-contract-auditor"}}