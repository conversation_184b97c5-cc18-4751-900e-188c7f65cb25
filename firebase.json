{"hosting": {"public": "frontend/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/", "destination": "/landing.html"}, {"source": "/landing", "destination": "/landing.html"}, {"source": "/app", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "max-age=0"}]}]}, "functions": {"source": "api", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"hosting": {"port": 5000}, "functions": {"port": 5001}, "ui": {"enabled": true}}}