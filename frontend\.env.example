# Frontend Environment Variables (Development)
# For Vite to include these in the build, they must be prefixed with VITE_

# API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_APP_NAME=FlashAudit
VITE_APP_VERSION=2.0.0

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your-clerk-publishable-key

# Development Settings
VITE_NODE_ENV=development
VITE_DEV_MODE=true
VITE_DEBUG_MODE=true

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Ethereum Testnet URLs (for testing)
SEPOLIA_RPC_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
POLYGON_MUMBAI_RPC_URL=https://polygon-mumbai.g.alchemy.com/v2/YOUR_ALCHEMY_KEY
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# TEE Monitor Configuration
TEE_LOG_ENABLED=true
TEE_LOG_PATH=./logs/auditLogs.json
TEE_ENCRYPTION_KEY=your-tee-encryption-key-here

# Audit Configuration
MAX_CONTRACT_SIZE_BYTES=1048576
AUDIT_TIMEOUT_MS=30000
VULNERABILITY_THRESHOLD_HIGH=80
VULNERABILITY_THRESHOLD_MEDIUM=50

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
