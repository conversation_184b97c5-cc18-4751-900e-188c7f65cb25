# Production Environment Variables for Frontend
NODE_ENV=production
VITE_NODE_ENV=production

# API Configuration
VITE_API_BASE_URL=https://flashaudit-public-kvkozkb4r.vercel.app/api
VITE_API_URL=https://flashaudit-public-kvkozkb4r.vercel.app/api
VITE_APP_NAME=FlashAudit
VITE_APP_VERSION=2.0.0

# Development Settings
VITE_DEV_MODE=false
VITE_DEBUG_MODE=false

# Authentication
VITE_ENABLE_AUTH=true
VITE_JWT_STORAGE_KEY=flashaudit_token

# Features
VITE_ENABLE_MULTI_CHAIN=true
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_TEAM_COLLABORATION=true
VITE_ENABLE_REAL_TIME_MONITORING=true

# Supabase Configuration
VITE_SUPABASE_URL=https://gqdbmvtgychgwztlbaus.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdxZGJtdnRneWNoZ3d6dGxiYXVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDc2MDAsImV4cCI6MjA2NjUyMzYwMH0.Q889SrVOiIFfKi2S9Ma4xVhjkAE3nKaE_B03G7S6Ibo

# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_ZnVua3ktcGFuZGEtNDYuY2xlcmsuYWNjb3VudHMuZGV2JA
