.chain-ide-container {
  display: grid;
  grid-template-columns: 48px 300px 1fr;
  grid-template-rows: 35px 1fr 180px;
  height: 100vh;
  background-color: #1e1e1e;
  color: #cccccc;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 13px;
}

/* Top Bar - VS Code Style */
.top-bar {
  grid-column: 1 / -1;
  grid-row: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  background-color: #323233;
  border-bottom: 1px solid #2d2d30;
  height: 35px;
}

.top-bar-left, .top-bar-center, .top-bar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 400;
  color: #cccccc;
  font-size: 13px;
}

.app-title svg {
  color: #007acc;
  font-size: 16px;
}

.back-to-dashboard {
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  margin-left: 16px;
}

.back-to-dashboard:hover {
  background-color: #2a2d2e;
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-address {
  font-family: monospace;
  background-color: #3c3c3c;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.connection-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.connection-status.connected {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

/* Sidebar - VS Code Style */
.sidebar {
  grid-column: 1 / 3;
  grid-row: 2;
  display: flex;
}

.sidebar-icons {
  width: 48px;
  background-color: #2c2c2c;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 4px;
  border-right: 1px solid #2d2d30;
}

.sidebar-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #858585;
  cursor: pointer;
  margin-bottom: 0;
  border-radius: 0;
  position: relative;
  font-size: 16px;
}

.sidebar-icon:hover {
  color: #cccccc;
}

.sidebar-icon.active {
  color: #ffffff;
}

.sidebar-icon.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #007acc;
}

.panel-content {
  flex: 1;
  background-color: #252526;
  border-right: 1px solid #2d2d30;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  margin: 0;
  padding: 8px 20px 8px 20px;
  letter-spacing: 0.4px;
}

/* Main Content */
.main-content {
  grid-column: 3;
  grid-row: 2;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.editor-tabs {
  height: 35px;
  background-color: #2d2d2d;
  display: flex;
  align-items: center;
  padding: 0 8px;
  border-bottom: 1px solid #404040;
}

.editor-tab {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #2d2d2d;
  color: #969696;
  cursor: pointer;
  border-right: 1px solid #404040;
}

.editor-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

.editor-container {
  flex: 1;
  overflow: hidden;
}

/* Bottom Panel - VS Code Style */
.bottom-panel {
  grid-column: 1 / -1;
  grid-row: 3;
  background-color: #1e1e1e;
  border-top: 1px solid #2d2d30;
}

.terminal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.terminal-header {
  background-color: #323233;
  padding: 8px 16px;
  border-bottom: 1px solid #2d2d30;
  font-size: 13px;
  color: #cccccc;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.terminal-actions {
  display: flex;
  gap: 8px;
}

.terminal-action-btn {
  background: #404040;
  border: 1px solid #555;
  color: #cccccc;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.terminal-action-btn:hover {
  background: #505050;
}

.terminal-output {
  flex: 1;
  padding: 8px 16px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-y: auto;
}

.terminal-line {
  color: #cccccc;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.terminal-line.terminal-info {
  color: #cccccc;
}

.terminal-line.terminal-success {
  color: #4caf50;
}

.terminal-line.terminal-warning {
  color: #ff9800;
}

.terminal-line.terminal-error {
  color: #f44336;
}

.terminal-timestamp {
  color: #888;
  font-size: 12px;
}

.terminal-source {
  color: #64b5f6;
  font-size: 12px;
  font-weight: bold;
}

.terminal-message {
  flex: 1;
}

/* Compiler Panel Styles */
.compiler-section {
  padding: 16px;
}

.compiler-config, .deploy-config {
  background: #2d2d30;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.config-header {
  font-weight: bold;
  margin-bottom: 12px;
  color: #cccccc;
}

.config-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  min-width: 80px;
  color: #cccccc;
  font-size: 13px;
}

.config-select, .config-input {
  background: #3c3c3c;
  border: 1px solid #555;
  color: #cccccc;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 13px;
}

.compiler-actions, .deploy-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.compile-btn, .deploy-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.compile-btn.primary, .deploy-btn.primary {
  background: #007acc;
  color: white;
}

.compile-btn.primary:hover, .deploy-btn.primary:hover {
  background: #005a9e;
}

.compile-btn.secondary, .deploy-btn.secondary {
  background: #404040;
  color: #cccccc;
  border: 1px solid #555;
}

.compile-btn.secondary:hover, .deploy-btn.secondary:hover {
  background: #505050;
}

.compiler-output {
  background: #1e1e1e;
  border: 1px solid #2d2d30;
  border-radius: 4px;
}

.output-header {
  background: #323233;
  padding: 8px 12px;
  border-bottom: 1px solid #2d2d30;
  font-size: 13px;
  color: #cccccc;
}

.output-content {
  padding: 8px 12px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
}

.output-line {
  margin-bottom: 4px;
}

.output-line.success {
  color: #4caf50;
}

.output-line.info {
  color: #cccccc;
}

.output-line.error {
  color: #f44336;
}

.panel-tabs {
  height: 35px;
  background-color: #2d2d2d;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.panel-tabs button {
  height: 100%;
  padding: 0 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
}

.panel-tabs button.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

/* Network Selector */
.network-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.network-selector select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

/* Wallet Connection - VS Code Style */
.wallet-connection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-options {
  display: flex;
  gap: 8px;
}

.connect-wallet {
  background-color: #007acc;
  color: #ffffff;
  border: none;
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.connect-wallet:hover {
  background-color: #1177bb;
  transform: translateY(-1px);
}

.connect-wallet.metamask {
  background-color: #f6851b;
}

.connect-wallet.metamask:hover {
  background-color: #e2761b;
}

.connect-wallet.phantom {
  background-color: #ab9ff2;
  color: #000000;
}

.connect-wallet.phantom:hover {
  background-color: #9c8df1;
}

.wallet-connected-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-connected {
  color: #4ec9b0;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.disconnect-wallet {
  background-color: #dc2626;
  color: #ffffff;
  border: none;
  padding: 2px 6px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.disconnect-wallet:hover {
  background-color: #b91c1c;
}

/* File Explorer */
.file-explorer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.explorer-header {
  padding: 8px 16px;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.explorer-content {
  flex: 1;
  padding: 8px;
}

.folder-node, .file-node {
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
}

.folder-node:hover, .file-node:hover {
  background-color: #37373d;
}

.file-node.selected {
  background-color: #37373d;
}

.folder-content {
  padding-left: 16px;
}

/* Enhanced File Explorer - VS Code Style */
.file-tree {
  padding: 0;
}

.project-root {
  margin-bottom: 0;
}

.project-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 400;
  margin-bottom: 4px;
  color: #cccccc;
  padding: 4px 8px 4px 20px;
  font-size: 13px;
}

.folder-item {
  margin-bottom: 0;
}

.folder-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 400;
  color: #cccccc;
  margin-bottom: 0;
  padding: 1px 8px 1px 20px;
  font-size: 13px;
  height: 22px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 1px 8px 1px 32px;
  color: #cccccc;
  transition: background-color 0.1s;
  font-size: 13px;
  height: 22px;
  cursor: pointer;
}

.file-item:hover {
  background-color: #2a2d2e;
}

.file-item.active {
  background-color: #37373d;
  color: #ffffff;
}

.no-project {
  text-align: center;
  color: #858585;
  padding: 32px 16px;
  font-size: 13px;
}

.no-project p {
  margin: 8px 0;
}

/* Editor */
.editor-textarea {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
  border: none;
  padding: 16px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
}

.editor-textarea:focus {
  outline: none;
}

/* File Editor - VS Code Style */
.file-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.file-tabs {
  display: flex;
  background-color: #2d2d30;
  border-bottom: 1px solid #2d2d30;
  min-height: 35px;
  overflow-x: auto;
}

.file-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  background-color: #2d2d30;
  color: #969696;
  cursor: pointer;
  border-right: 1px solid #252526;
  font-size: 13px;
  position: relative;
  height: 35px;
  min-width: 120px;
  white-space: nowrap;
}

.file-tab:hover {
  background-color: #323233;
  color: #cccccc;
}

.file-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
  border-bottom: 1px solid #1e1e1e;
}

.close-tab {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  font-size: 14px;
  line-height: 1;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-tab:hover {
  background-color: #464647;
}

.file-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #323233;
  border-bottom: 1px solid #2d2d30;
  font-size: 12px;
  height: 22px;
}

.file-path {
  color: #858585;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.save-btn {
  background-color: #007acc;
  color: #ffffff;
  border: none;
  padding: 2px 6px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
}

.save-btn:hover {
  background-color: #1177bb;
}

/* Code editor styles moved to line numbers section */

.code-editor:focus {
  outline: none;
}

.no-file-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #858585;
  background-color: #1e1e1e;
}

.no-file-selected h3 {
  color: #cccccc;
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
}

.project-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.action-btn {
  background-color: #007acc;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 400;
}

.action-btn:hover {
  background-color: #1177bb;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #858585;
  background-color: #1e1e1e;
  padding: 40px;
}

.welcome-message h2 {
  color: #cccccc;
  font-weight: 400;
  font-size: 24px;
  margin-bottom: 16px;
}

.welcome-message p {
  font-size: 14px;
  margin-bottom: 24px;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.feature-item {
  font-size: 13px;
  color: #cccccc;
}

.get-started {
  font-size: 14px;
  color: #858585;
}

/* Templates View Styles */
.top-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 400px;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 8px;
  color: #858585;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: linear-gradient(145deg, #3c3c3c 0%, #383838 100%);
  border: 2px solid transparent;
  color: #ffffff;
  padding: 12px 16px 12px 40px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  outline: none;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #007acc;
  background: linear-gradient(145deg, #404040 0%, #3c3c3c 100%);
  box-shadow:
    0 4px 16px rgba(0, 122, 204, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.search-input::placeholder {
  color: #888888;
  font-weight: 400;
}

.templates-sidebar-content {
  padding: 8px 0;
}

.sidebar-section h4 {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  margin: 0 0 8px 0;
  padding: 0 20px;
  letter-spacing: 0.4px;
}

/* Enhanced Section Toggle */
.section-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(145deg, #3c3c3c 0%, #383838 100%);
  border: 1px solid #404040;
  border-radius: 4px;
  color: #cccccc;
  font-size: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 8px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.section-toggle:hover {
  background: linear-gradient(145deg, #404040 0%, #3c3c3c 100%);
  border-color: #007acc;
  color: #ffffff;
  transform: scale(1.05);
  box-shadow:
    0 2px 6px rgba(0, 122, 204, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.section-toggle:active {
  transform: scale(0.95);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Section Header with Toggle */
.section-header-with-toggle {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(145deg, #2d2d2d 0%, #2a2a2a 100%);
  border-bottom: 1px solid #404040;
  margin-bottom: 8px;
}

.section-header-with-toggle h4 {
  margin: 0;
  padding: 0;
  flex: 1;
}

.network-list {
  display: flex;
  flex-direction: column;
}

.network-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 20px;
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  font-size: 13px;
  text-align: left;
  height: 22px;
}

.network-item:hover {
  background-color: #2a2d2e;
}

.network-item.active {
  background-color: #37373d;
  color: #ffffff;
}

.network-icon {
  font-size: 14px;
}

.network-name {
  flex: 1;
}

.templates-main-content {
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #2d2d30;
}

.section-header h2 {
  font-size: 16px;
  font-weight: 600;
  color: #cccccc;
  margin: 0;
}

.template-count {
  font-size: 12px;
  color: #858585;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.template-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.template-card-content {
  padding: 20px 18px 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.template-icon {
  color: #007acc;
  font-size: 28px;
  background: #232f3e;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
}

.template-version {
  font-size: 11px;
  color: #858585;
  background-color: #232f3e;
  padding: 2px 8px;
  border-radius: 2px;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
}

.template-info p {
  font-size: 13px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #232f3e;
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: transparent;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.template-network {
  background: #232f3e;
  color: transparent;
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Statistics Panel */
.refresh-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  color: #969696;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #404040;
}

.stat-card.primary {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.stat-card.critical {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
}

.stat-card.warning {
  border-color: #ea580c;
  background-color: rgba(234, 88, 12, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.recent-activity {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #969696;
  font-family: monospace;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.activity-label {
  font-size: 12px;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
}

.activity-score.good { color: #4ade80; }
.activity-score.medium { color: #fbbf24; }
.activity-score.poor { color: #ef4444; }

.security-trends {
  margin-bottom: 24px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-label {
  flex: 1;
  font-size: 12px;
  color: #969696;
}

.trend-bar {
  flex: 2;
  height: 6px;
  background-color: #404040;
  border-radius: 3px;
  overflow: hidden;
}

.trend-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend-fill.good { background-color: #4ade80; }
.trend-fill.medium { background-color: #fbbf24; }
.trend-fill.poor { background-color: #ef4444; }

.trend-value {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.quick-stats {
  margin-bottom: 16px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.quick-stat-label {
  font-size: 12px;
  color: #969696;
}

.quick-stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.no-stats, .no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #969696;
  gap: 16px;
}

.load-stats-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* Network Panel */
.add-network-btn {
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-network-btn:hover:not(:disabled) {
  background-color: #15803d;
}

.add-network-btn:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.current-network {
  margin-bottom: 24px;
}

.network-card {
  background-color: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-card:hover {
  background-color: #37373d;
  border-color: #0e639c;
}

.network-card.active {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.network-card.current {
  cursor: default;
  border-color: #16a34a;
  background-color: rgba(22, 163, 74, 0.1);
}

.network-info {
  flex: 1;
}

.network-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969696;
}

.chain-id {
  font-family: monospace;
}

.network-type.testnet {
  color: #fbbf24;
}

.network-type.mainnet {
  color: #4ade80;
}

.network-rpc {
  font-size: 11px;
  color: #969696;
  font-family: monospace;
  margin-top: 4px;
  word-break: break-all;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.network-status.online .status-indicator {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

.network-status.offline .status-indicator {
  background-color: #ef4444;
}

.network-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #0e639c;
  border-color: #0e639c;
  color: #ffffff;
}

.delete-btn:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.save-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.cancel-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.network-edit-form, .network-add-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.network-edit-form input, .network-add-form input {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.network-edit-form label, .network-add-form label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #d4d4d4;
}

.edit-actions, .add-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Enhanced Terminal */
.terminal-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.terminal-line {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chain-ide-container {
    grid-template-columns: 48px 200px 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-bar-center {
    display: none;
  }
}

/* Global Reset for chainIDE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #d4d4d4;
  background: #1e1e1e;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Additional Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-8 { gap: 8px; }
.gap-16 { gap: 16px; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0e639c;
  outline-offset: 2px;
}

/* Transitions */
button,
.network-card,
.stat-card,
.vulnerability-item {
  transition: all 0.2s ease;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}

/* VS Code Style Panel Components */
.vscode-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #252526;
  color: #cccccc;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 13px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  background-color: #252526;
  border-bottom: 1px solid #2d2d30;
  min-height: 35px;
}

.panel-title {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  letter-spacing: 0.4px;
}

.header-action {
  background: none;
  border: none;
  color: #858585;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-action:hover {
  background-color: #2a2d2e;
  color: #cccccc;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Explorer Panel Styles */
.explorer-section {
  padding: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 20px 4px 8px;
  background-color: #252526;
  border-bottom: 1px solid #2d2d30;
  min-height: 22px;
}

.section-toggle {
  color: #cccccc;
  font-size: 12px;
  margin-right: 6px;
  cursor: pointer;
}

.section-title {
  font-size: 13px;
  font-weight: 400;
  color: #cccccc;
  flex: 1;
}

.section-actions {
  display: flex;
  gap: 2px;
  opacity: 1; /* Always visible for better UX */
  transition: opacity 0.2s;
}

.section-header:hover .section-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  font-size: 14px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #37373d;
  color: #ffffff;
  transform: scale(1.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.file-tree {
  padding: 0;
}

.folder-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 1px 8px 1px 20px;
  height: 22px;
  color: #cccccc;
  font-size: 13px;
  cursor: pointer;
}

.folder-header:hover {
  background-color: #2a2d2e;
}

.folder-icon, .file-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

.folder-name, .file-name {
  font-size: 13px;
  color: #cccccc;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 1px 8px 1px 32px;
  height: 22px;
  color: #cccccc;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.1s;
}

.file-item:hover {
  background-color: #2a2d2e;
}

.file-item.active {
  background-color: #37373d;
  color: #ffffff;
}

.file-item.nested {
  padding-left: 44px;
}

.file-item.selected,
.folder-header.selected {
  background-color: #094771 !important;
  color: #ffffff;
}

.context-menu {
  position: fixed;
  background-color: #2d2d30;
  border: 1px solid #464647;
  border-radius: 3px;
  padding: 4px 0;
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  z-index: 1000;
}

.context-menu-item {
  padding: 6px 12px;
  cursor: pointer;
  font-size: 13px;
  color: #cccccc;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.1s;
}

.context-menu-item:hover {
  background-color: #37373d;
}

/* Code Editor with Line Numbers */
.code-editor-container {
  display: flex;
  flex: 1;
  background-color: #1e1e1e;
  border: 1px solid #2d2d30;
  border-radius: 3px;
  overflow: hidden;
}

.line-numbers {
  background-color: #1e1e1e;
  color: #858585;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 16px 10px 10px;
  border-right: 1px solid #2d2d30;
  user-select: none;
  min-width: 60px;
  text-align: right;
  overflow: hidden;
  white-space: nowrap;
  box-sizing: border-box;
}

.line-number {
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 13px;
  color: #858585;
  padding-right: 4px;
  transition: color 0.1s ease;
}

.line-number:hover {
  color: #cccccc;
}

.line-number.current-line {
  color: #ffffff;
  background-color: #2d2d30;
  font-weight: 500;
}

.code-editor {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  border: none;
  outline: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 20px;
  padding: 10px;
  resize: none;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
  tab-size: 2;
}

.code-editor:focus {
  outline: none;
}

.code-editor::placeholder {
  color: #6a6a6a;
}

/* VS Code-like editor improvements */
.code-editor::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.code-editor::-webkit-scrollbar-track {
  background-color: #1e1e1e;
}

.code-editor::-webkit-scrollbar-thumb {
  background-color: #424242;
  border-radius: 7px;
  border: 3px solid #1e1e1e;
}

.code-editor::-webkit-scrollbar-thumb:hover {
  background-color: #4f4f4f;
}

.code-editor::-webkit-scrollbar-corner {
  background-color: #1e1e1e;
}

/* Line numbers scrollbar */
.line-numbers::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* Plugin Manager Styles */
.search-box {
  padding: 8px 20px;
  border-bottom: 1px solid #2d2d30;
}

.search-input {
  width: 100%;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  color: #cccccc;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 13px;
  outline: none;
}

.search-input:focus {
  border-color: #007acc;
}

.search-input::placeholder {
  color: #858585;
}

.plugin-categories {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  border-bottom: 1px solid #2d2d30;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 4px 20px;
  color: #cccccc;
  cursor: pointer;
  font-size: 13px;
  height: 22px;
  transition: background-color 0.1s;
}

.category-item:hover {
  background-color: #2a2d2e;
}

.category-item.active {
  background-color: #37373d;
  color: #ffffff;
}

.plugin-list {
  padding: 8px 0;
}

.plugin-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 20px;
  border-bottom: 1px solid #2d2d30;
  transition: background-color 0.1s;
}

.plugin-item:hover {
  background-color: #2a2d2e;
}

.plugin-icon {
  font-size: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3c3c3c;
  border-radius: 4px;
  color: #cccccc;
}

.plugin-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.plugin-name {
  font-size: 13px;
  font-weight: 500;
  color: #cccccc;
}

.plugin-description {
  font-size: 12px;
  color: #858585;
}

.plugin-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #858585;
}

.plugin-author {
  color: #858585;
}

.plugin-downloads {
  color: #858585;
}

.plugin-action {
  background: none;
  border: 1px solid #464647;
  color: #cccccc;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  min-width: 60px;
}

.plugin-action.installed {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.plugin-action.update {
  background-color: #ea580c;
  border-color: #ea580c;
  color: #ffffff;
}

.plugin-action:hover {
  background-color: #464647;
}

.plugin-action.installed:hover {
  background-color: #15803d;
}

.plugin-action.update:hover {
  background-color: #dc2626;
}

/* Port Manager Styles */
.port-list {
  padding: 8px 0;
}

.port-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 20px;
  border-bottom: 1px solid #2d2d30;
  transition: background-color 0.1s;
}

.port-item:hover {
  background-color: #2a2d2e;
}

.port-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.port-number {
  font-size: 13px;
  font-weight: 500;
  color: #cccccc;
  font-family: 'Consolas', 'Courier New', monospace;
}

.port-description {
  font-size: 12px;
  color: #858585;
}

.port-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 60px;
  justify-content: center;
}

.port-status.running {
  background-color: rgba(22, 163, 74, 0.2);
  color: #4ade80;
}

.port-status.stopped {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
}

.port-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.port-item:hover .port-actions {
  opacity: 1;
}

/* Sandbox Management Styles */
.sandbox-list {
  padding: 8px 0;
}

.sandbox-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  border-bottom: 1px solid #2d2d30;
  transition: background-color 0.1s;
}

.sandbox-item:hover {
  background-color: #2a2d2e;
}

.sandbox-item.active {
  background-color: rgba(14, 99, 156, 0.1);
  border-left: 3px solid #007acc;
}

.sandbox-icon {
  font-size: 12px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sandbox-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sandbox-name {
  font-size: 13px;
  font-weight: 500;
  color: #cccccc;
}

.sandbox-description {
  font-size: 12px;
  color: #858585;
}

.sandbox-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #858585;
}

.sandbox-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.sandbox-item:hover .sandbox-actions {
  opacity: 1;
}

/* Git Manager Styles */
.git-section {
  padding: 8px 0;
}

.git-branch {
  padding: 8px 20px;
  border-bottom: 1px solid #2d2d30;
}

.branch-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.branch-icon {
  font-size: 14px;
  color: #4ade80;
}

.branch-name {
  font-size: 13px;
  font-weight: 500;
  color: #cccccc;
  flex: 1;
}

.branch-action {
  background: none;
  border: 1px solid #464647;
  color: #858585;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.branch-action:hover {
  background-color: #464647;
  color: #cccccc;
}

.git-changes {
  padding: 8px 0;
}

.changes-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  letter-spacing: 0.4px;
  border-bottom: 1px solid #2d2d30;
}

.changes-actions {
  display: flex;
  gap: 4px;
}

.changes-list {
  padding: 4px 0;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 20px;
  transition: background-color 0.1s;
}

.change-item:hover {
  background-color: #2a2d2e;
}

.change-status {
  font-size: 11px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: #ffffff;
}

.change-status.modified {
  background-color: #ea580c;
}

.change-status.added {
  background-color: #16a34a;
}

.change-status.deleted {
  background-color: #dc2626;
}

.change-file {
  font-size: 13px;
  color: #cccccc;
  flex: 1;
}

.change-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.change-item:hover .change-actions {
  opacity: 1;
}

.git-commit {
  padding: 12px 20px;
  border-top: 1px solid #2d2d30;
}

.commit-message {
  width: 100%;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  color: #cccccc;
  padding: 8px;
  border-radius: 4px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  outline: none;
  margin-bottom: 8px;
}

.commit-message:focus {
  border-color: #007acc;
}

.commit-message::placeholder {
  color: #858585;
}

.commit-btn {
  width: 100%;
  background-color: #007acc;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
}

.commit-btn:hover {
  background-color: #1177bb;
}

.commit-btn:disabled {
  background-color: #464647;
  color: #858585;
  cursor: not-allowed;
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #858585;
  font-size: 13px;
}

/* Simple App Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: #d4d4d4;
}

.welcome-message h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #0e639c;
}

.welcome-message p {
  font-size: 18px;
  margin-bottom: 24px;
  color: #969696;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 24px 0;
  max-width: 600px;
}

.feature-item {
  background: #2d2d2d;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #0e639c;
  font-size: 14px;
}

.get-started {
  font-size: 16px;
  color: #4ade80 !important;
  font-weight: 500;
  margin-top: 24px;
}

.panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.file-tree {
  margin-top: 16px;
}

.folder-item, .file-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .file-item:hover {
  background-color: #3c3c3c;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compiler-settings label {
  font-size: 12px;
  color: #969696;
}

.compiler-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.compile-btn, .connect-btn, .deploy-btn, .audit-btn {
  background: #0e639c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.compile-btn:hover, .connect-btn:hover, .deploy-btn:hover, .audit-btn:hover {
  background: #1177bb;
}

.deploy-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.deploy-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deploy-info p {
  margin: 0;
  font-size: 14px;
}

.audit-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-results {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #404040;
}

.stat-value {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.network-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-settings label {
  font-size: 12px;
  color: #969696;
}

.network-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.network-info {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.network-info p {
  margin: 4px 0;
  font-size: 14px;
}

.wallet-connected {
  color: #4ade80;
  font-size: 12px;
}

.terminal-header {
  background: #2d2d2d;
  padding: 8px 16px;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  color: #969696;
}

.terminal-output {
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.terminal-line {
  margin-bottom: 4px;
  color: #d4d4d4;
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: #0e639c;
  font-size: 20px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #969696;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: #3c3c3c;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px 8px 36px;
  color: #d4d4d4;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #0e639c;
}

.search-input::placeholder {
  color: #969696;
}

.header-right {
  display: flex;
  align-items: center;
}

.sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.sign-in-btn:hover {
  background: #1177bb;
}

.dashboard-content {
  padding: 24px;
}

.projects-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.new-project-btn {
  background: #16a34a;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.new-project-btn:hover {
  background: #15803d;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.project-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.project-card.new-project {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  border: 2px dashed #404040;
  background: transparent;
}

.project-card.new-project:hover {
  border-color: #16a34a;
  background: rgba(22, 163, 74, 0.05);
}

.new-project-icon {
  font-size: 32px;
  color: #969696;
  margin-bottom: 12px;
}

.project-card.new-project:hover .new-project-icon {
  color: #16a34a;
}

.new-project-text {
  font-size: 16px;
  color: #969696;
  font-weight: 500;
}

.project-card.new-project:hover .new-project-text {
  color: #16a34a;
}

.project-preview {
  height: 120px;
  background: linear-gradient(135deg, #2d2d2d 0%, #3c3c3c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chainide-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #0e639c;
  opacity: 0.7;
}

.project-info {
  padding: 16px;
}

.project-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.project-date {
  font-size: 12px;
  color: #969696;
}

.project-menu {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.project-menu:hover {
  background: #404040;
  color: #ffffff;
}

.project-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-icon {
  color: #0e639c;
  font-size: 14px;
}

.project-name {
  font-size: 14px;
  color: #d4d4d4;
  font-weight: 500;
}

.back-to-dashboard {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  margin-left: 16px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.back-to-dashboard:hover {
  background: #404040;
  color: #ffffff;
}

/* Templates View Styles - Enhanced */
.templates-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #1e1e1e 100%);
  color: #d4d4d4;
  display: flex;
  flex-direction: column;
}

.templates-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  background: linear-gradient(135deg, #2d2d2d 0%, #323233 100%);
  border-bottom: 1px solid #404040;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.templates-header .header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-btn:hover {
  background: #404040;
  color: #ffffff;
}

.templates-header h1 {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.templates-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.templates-sidebar {
  width: 320px;
  background: linear-gradient(135deg, #252526 0%, #2a2a2b 100%);
  border-right: 1px solid #404040;
  padding: 20px;
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-section h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.network-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.network-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #d4d4d4;
  cursor: pointer;
  border-radius: 8px;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 4px;
}

.network-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  border-radius: 0 3px 3px 0;
  transition: background 0.3s ease;
}

.network-item:hover {
  background: linear-gradient(145deg, #37373d 0%, #323233 100%);
  color: #ffffff;
  transform: translateX(4px);
}

.network-item:hover::before {
  background: #007acc;
}

.network-item.active {
  background: #37373d;
  color: #ffffff;
}

.network-item.active::before {
  background: transparent;
}

.network-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.network-item:hover .network-icon {
  transform: scale(1.1);
}

.network-name {
  flex: 1;
}

.network-count {
  margin-left: auto;
  font-size: 11px;
  color: #ffffff;
  background: linear-gradient(145deg, #4da2ff 0%, #007acc 100%);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  box-shadow:
    0 2px 4px rgba(0, 122, 204, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.network-item.active .network-count {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.templates-main {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background: linear-gradient(135deg, #1e1e1e 0%, #1a1a1a 100%);
}

.templates-section {
  max-width: 1400px;
  margin: 0 auto;
}

.templates-section .section-header {
  margin-bottom: 32px;
  text-align: center;
}

.templates-section .section-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.templates-section .section-header p {
  font-size: 16px;
  color: #b3b3b3;
  margin: 0;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  padding: 0 16px;
}

.template-card {
  background: linear-gradient(145deg, #2a2a2b 0%, #232526 50%, #1e1e1f 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  backdrop-filter: blur(10px);
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(0, 122, 204, 0.05) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover::before {
  opacity: 1;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow:
    0 8px 32px rgba(0, 122, 204, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-4px) scale(1.02);
  background: linear-gradient(145deg, #2d2d2e 0%, #252526 50%, #202021 100%);
}

.template-card-content {
  padding: 24px 22px 20px 22px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  position: relative;
  z-index: 1;
}

.template-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.template-icon {
  color: #007acc;
  font-size: 32px;
  background: linear-gradient(145deg, #2d2d2e 0%, #232526 100%);
  border-radius: 12px;
  padding: 12px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.template-card:hover .template-icon {
  color: #4da2ff;
  transform: scale(1.05);
  box-shadow:
    0 6px 16px rgba(0, 122, 204, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.template-version {
  font-size: 11px;
  color: #b3b3b3;
  background: linear-gradient(145deg, #2d2d2e 0%, #232526 100%);
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.template-info h3 {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.3;
}

.template-info p {
  font-size: 14px;
  color: #b8b8b8;
  line-height: 1.6;
  margin: 0;
  flex: 1;
  opacity: 0.9;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: linear-gradient(145deg, #007acc 0%, #0066aa 100%);
  padding: 6px 12px;
  border-radius: 16px;
  margin-right: 6px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  box-shadow:
    0 2px 6px rgba(0, 122, 204, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.template-category:hover,
.template-network:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 122, 204, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.template-network {
  background: linear-gradient(145deg, #2d2d2e 0%, #232526 100%);
  color: #4da2ff;
  box-shadow:
    0 2px 6px rgba(77, 162, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.template-network:hover {
  box-shadow:
    0 4px 12px rgba(77, 162, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Loading Animation for Templates */
@keyframes templateShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.template-loading {
  background: linear-gradient(90deg, #2a2a2b 0%, #323233 50%, #2a2a2b 100%);
  background-size: 200px 100%;
  animation: templateShimmer 1.5s infinite;
}

/* Enhanced Responsive Design */
@media (max-width: 1400px) {
  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .templates-sidebar {
    width: 280px;
  }

  .templates-main {
    padding: 24px;
  }

  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .templates-header {
    padding: 12px 16px;
  }

  .templates-content {
    flex-direction: column;
  }

  .templates-sidebar {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #404040;
  }

  .templates-main {
    padding: 16px;
  }

  .templates-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .template-card {
    min-height: 200px;
  }
}

@media (max-width: 480px) {
  .templates-header h1 {
    font-size: 20px;
  }

  .template-card-content {
    padding: 18px 16px;
  }

  .template-info h3 {
    font-size: 16px;
  }

  .template-info p {
    font-size: 13px;
  }
}

/* Template Card Enhancements */
.template-card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #007acc, #4da2ff, #007acc);
  border-radius: 18px;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.template-card:hover .template-card-glow {
  opacity: 0.3;
}

/* Template Categories with Colors */
.template-category.nft {
  background: linear-gradient(145deg, #8b5cf6 0%, #7c3aed 100%);
}

.template-category.defi {
  background: linear-gradient(145deg, #10b981 0%, #059669 100%);
}

.template-category.gaming {
  background: linear-gradient(145deg, #f59e0b 0%, #d97706 100%);
}

.template-category.marketplace {
  background: linear-gradient(145deg, #ef4444 0%, #dc2626 100%);
}

.template-category.basic {
  background: linear-gradient(145deg, #6b7280 0%, #4b5563 100%);
}

.template-category.enterprise {
  background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
}

/* Network-specific styling */
.template-network.polygon {
  background: linear-gradient(145deg, #8247e5 0%, #6f42c1 100%);
}

.template-network.ethereum {
  background: linear-gradient(145deg, #627eea 0%, #4f6bd5 100%);
}

.template-network.sui {
  background: linear-gradient(145deg, #4da6ff 0%, #007acc 100%);
}

.template-network.aptos {
  background: linear-gradient(145deg, #00d4aa 0%, #00b894 100%);
}

.template-network.flow {
  background: linear-gradient(145deg, #00ef8b 0%, #00d26a 100%);
}

/* Enhanced Import Card */
.import-card {
  background: linear-gradient(145deg, #2a2a2b 0%, #232526 50%, #1e1e1f 100%);
  border: 2px dashed #404040;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.import-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(0, 122, 204, 0.05) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.import-card:hover {
  border-color: #007acc;
  background: linear-gradient(145deg, #2d2d2e 0%, #252526 50%, #202021 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 32px rgba(0, 122, 204, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.import-card:hover::before {
  opacity: 1;
}

.import-card h3 {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.import-card p {
  font-size: 14px;
  color: #b8b8b8;
  margin: 0;
  line-height: 1.5;
}

.import-icon {
  font-size: 48px;
  color: #007acc;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.import-card:hover .import-icon {
  transform: scale(1.1) rotate(5deg);
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Statistics Panel */
.refresh-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  color: #969696;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #404040;
}

.stat-card.primary {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.stat-card.critical {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
}

.stat-card.warning {
  border-color: #ea580c;
  background-color: rgba(234, 88, 12, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.recent-activity {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #969696;
  font-family: monospace;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.activity-label {
  font-size: 12px;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
}

.activity-score.good { color: #4ade80; }
.activity-score.medium { color: #fbbf24; }
.activity-score.poor { color: #ef4444; }

.security-trends {
  margin-bottom: 24px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-label {
  flex: 1;
  font-size: 12px;
  color: #969696;
}

.trend-bar {
  flex: 2;
  height: 6px;
  background-color: #404040;
  border-radius: 3px;
  overflow: hidden;
}

.trend-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend-fill.good { background-color: #4ade80; }
.trend-fill.medium { background-color: #fbbf24; }
.trend-fill.poor { background-color: #ef4444; }

.trend-value {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.quick-stats {
  margin-bottom: 16px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.quick-stat-label {
  font-size: 12px;
  color: #969696;
}

.quick-stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.no-stats, .no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #969696;
  gap: 16px;
}

.load-stats-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* Network Panel */
.add-network-btn {
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-network-btn:hover:not(:disabled) {
  background-color: #15803d;
}

.add-network-btn:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.current-network {
  margin-bottom: 24px;
}

.network-card {
  background-color: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-card:hover {
  background-color: #37373d;
  border-color: #0e639c;
}

.network-card.active {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.network-card.current {
  cursor: default;
  border-color: #16a34a;
  background-color: rgba(22, 163, 74, 0.1);
}

.network-info {
  flex: 1;
}

.network-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969696;
}

.chain-id {
  font-family: monospace;
}

.network-type.testnet {
  color: #fbbf24;
}

.network-type.mainnet {
  color: #4ade80;
}

.network-rpc {
  font-size: 11px;
  color: #969696;
  font-family: monospace;
  margin-top: 4px;
  word-break: break-all;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.network-status.online .status-indicator {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

.network-status.offline .status-indicator {
  background-color: #ef4444;
}

.network-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #0e639c;
  border-color: #0e639c;
  color: #ffffff;
}

.delete-btn:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.save-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.cancel-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.network-edit-form, .network-add-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.network-edit-form input, .network-add-form input {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.network-edit-form label, .network-add-form label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #d4d4d4;
}

.edit-actions, .add-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Enhanced Terminal */
.terminal-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.terminal-line {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chain-ide-container {
    grid-template-columns: 48px 200px 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-bar-center {
    display: none;
  }
}

/* Global Reset for chainIDE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #d4d4d4;
  background: #1e1e1e;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Additional Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-8 { gap: 8px; }
.gap-16 { gap: 16px; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0e639c;
  outline-offset: 2px;
}

/* Transitions */
button,
.network-card,
.stat-card,
.vulnerability-item {
  transition: all 0.2s ease;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}

/* Simple App Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: #d4d4d4;
}

.welcome-message h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #0e639c;
}

.welcome-message p {
  font-size: 18px;
  margin-bottom: 24px;
  color: #969696;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 24px 0;
  max-width: 600px;
}

.feature-item {
  background: #2d2d2d;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #0e639c;
  font-size: 14px;
}

.get-started {
  font-size: 16px;
  color: #4ade80 !important;
  font-weight: 500;
  margin-top: 24px;
}

.panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.file-tree {
  margin-top: 16px;
}

.folder-item, .file-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .file-item:hover {
  background-color: #3c3c3c;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compiler-settings label {
  font-size: 12px;
  color: #969696;
}

.compiler-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.compile-btn, .connect-btn, .deploy-btn, .audit-btn {
  background: #0e639c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.compile-btn:hover, .connect-btn:hover, .deploy-btn:hover, .audit-btn:hover {
  background: #1177bb;
}

.deploy-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.deploy-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deploy-info p {
  margin: 0;
  font-size: 14px;
}

.audit-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-results {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #404040;
}

.stat-value {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.network-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-settings label {
  font-size: 12px;
  color: #969696;
}

.network-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.network-info {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.network-info p {
  margin: 4px 0;
  font-size: 14px;
}

.wallet-connected {
  color: #4ade80;
  font-size: 12px;
}

.terminal-header {
  background: #2d2d2d;
  padding: 8px 16px;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  color: #969696;
}

.terminal-output {
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.terminal-line {
  margin-bottom: 4px;
  color: #d4d4d4;
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: #0e639c;
  font-size: 20px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #969696;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: #3c3c3c;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px 8px 36px;
  color: #d4d4d4;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #0e639c;
}

.search-input::placeholder {
  color: #969696;
}

.header-right {
  display: flex;
  align-items: center;
}

.sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.sign-in-btn:hover {
  background: #1177bb;
}

.dashboard-content {
  padding: 24px;
}

.projects-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.new-project-btn {
  background: #16a34a;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.new-project-btn:hover {
  background: #15803d;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.project-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.project-card.new-project {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  border: 2px dashed #404040;
  background: transparent;
}

.project-card.new-project:hover {
  border-color: #16a34a;
  background: rgba(22, 163, 74, 0.05);
}

.new-project-icon {
  font-size: 32px;
  color: #969696;
  margin-bottom: 12px;
}

.project-card.new-project:hover .new-project-icon {
  color: #16a34a;
}

.new-project-text {
  font-size: 16px;
  color: #969696;
  font-weight: 500;
}

.project-card.new-project:hover .new-project-text {
  color: #16a34a;
}

.project-preview {
  height: 120px;
  background: linear-gradient(135deg, #2d2d2d 0%, #3c3c3c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chainide-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #0e639c;
  opacity: 0.7;
}

.project-info {
  padding: 16px;
}

.project-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.project-date {
  font-size: 12px;
  color: #969696;
}

.project-menu {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.project-menu:hover {
  background: #404040;
  color: #ffffff;
}

.project-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-icon {
  color: #0e639c;
  font-size: 14px;
}

.project-name {
  font-size: 14px;
  color: #d4d4d4;
  font-weight: 500;
}

.back-to-dashboard {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  margin-left: 16px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.back-to-dashboard:hover {
  background: #404040;
  color: #ffffff;
}

/* Templates View Styles */
.templates-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
  display: flex;
  flex-direction: column;
}

.templates-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.templates-header .header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-btn:hover {
  background: #404040;
  color: #ffffff;
}

.templates-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.templates-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.templates-sidebar {
  width: 280px;
  background: #252526;
  border-right: 1px solid #404040;
  padding: 16px;
  overflow-y: auto;
}

.sidebar-section h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.network-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.network-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #d4d4d4;
  cursor: pointer;
  border-radius: 6px;
  text-align: left;
  font-size: 14px;
  width: 100%;
}

.network-item:hover {
  background: #3c3c3c;
}

.network-item.active {
  background: #37373d;
  color: #ffffff;
}

.network-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.network-name {
  flex: 1;
}

.templates-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.templates-section {
  max-width: 1200px;
  margin: 0 auto;
}

.templates-section .section-header {
  margin-bottom: 24px;
}

.templates-section .section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.template-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.template-card-content {
  padding: 20px 18px 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.template-icon {
  color: #007acc;
  font-size: 28px;
  background: #232f3e;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
}

.template-version {
  font-size: 11px;
  color: #858585;
  background-color: #232f3e;
  padding: 2px 8px;
  border-radius: 2px;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
}

.template-info p {
  font-size: 13px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #232f3e;
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: #007acc;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.template-network {
  background: #232f3e;
  color: #4da2ff;
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Statistics Panel */
.refresh-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  color: #969696;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #404040;
}

.stat-card.primary {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.stat-card.critical {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
}

.stat-card.warning {
  border-color: #ea580c;
  background-color: rgba(234, 88, 12, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.recent-activity {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #969696;
  font-family: monospace;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.activity-label {
  font-size: 12px;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
}

.activity-score.good { color: #4ade80; }
.activity-score.medium { color: #fbbf24; }
.activity-score.poor { color: #ef4444; }

.security-trends {
  margin-bottom: 24px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-label {
  flex: 1;
  font-size: 12px;
  color: #969696;
}

.trend-bar {
  flex: 2;
  height: 6px;
  background-color: #404040;
  border-radius: 3px;
  overflow: hidden;
}

.trend-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend-fill.good { background-color: #4ade80; }
.trend-fill.medium { background-color: #fbbf24; }
.trend-fill.poor { background-color: #ef4444; }

.trend-value {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.quick-stats {
  margin-bottom: 16px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.quick-stat-label {
  font-size: 12px;
  color: #969696;
}

.quick-stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.no-stats, .no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #969696;
  gap: 16px;
}

.load-stats-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* Network Panel */
.add-network-btn {
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-network-btn:hover:not(:disabled) {
  background-color: #15803d;
}

.add-network-btn:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.current-network {
  margin-bottom: 24px;
}

.network-card {
  background-color: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-card:hover {
  background-color: #37373d;
  border-color: #0e639c;
}

.network-card.active {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.network-card.current {
  cursor: default;
  border-color: #16a34a;
  background-color: rgba(22, 163, 74, 0.1);
}

.network-info {
  flex: 1;
}

.network-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969696;
}

.chain-id {
  font-family: monospace;
}

.network-type.testnet {
  color: #fbbf24;
}

.network-type.mainnet {
  color: #4ade80;
}

.network-rpc {
  font-size: 11px;
  color: #969696;
  font-family: monospace;
  margin-top: 4px;
  word-break: break-all;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.network-status.online .status-indicator {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

.network-status.offline .status-indicator {
  background-color: #ef4444;
}

.network-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #0e639c;
  border-color: #0e639c;
  color: #ffffff;
}

.delete-btn:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.save-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.cancel-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.network-edit-form, .network-add-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.network-edit-form input, .network-add-form input {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.network-edit-form label, .network-add-form label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #d4d4d4;
}

.edit-actions, .add-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Enhanced Terminal */
.terminal-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.terminal-line {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chain-ide-container {
    grid-template-columns: 48px 200px 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-bar-center {
    display: none;
  }
}

/* Global Reset for chainIDE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #d4d4d4;
  background: #1e1e1e;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Additional Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-8 { gap: 8px; }
.gap-16 { gap: 16px; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0e639c;
  outline-offset: 2px;
}

/* Transitions */
button,
.network-card,
.stat-card,
.vulnerability-item {
  transition: all 0.2s ease;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}

/* Simple App Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: #d4d4d4;
}

.welcome-message h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #0e639c;
}

.welcome-message p {
  font-size: 18px;
  margin-bottom: 24px;
  color: #969696;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 24px 0;
  max-width: 600px;
}

.feature-item {
  background: #2d2d2d;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #0e639c;
  font-size: 14px;
}

.get-started {
  font-size: 16px;
  color: #4ade80 !important;
  font-weight: 500;
  margin-top: 24px;
}

.panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.file-tree {
  margin-top: 16px;
}

.folder-item, .file-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .file-item:hover {
  background-color: #3c3c3c;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compiler-settings label {
  font-size: 12px;
  color: #969696;
}

.compiler-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.compile-btn, .connect-btn, .deploy-btn, .audit-btn {
  background: #0e639c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.compile-btn:hover, .connect-btn:hover, .deploy-btn:hover, .audit-btn:hover {
  background: #1177bb;
}

.deploy-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.deploy-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deploy-info p {
  margin: 0;
  font-size: 14px;
}

.audit-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-results {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #404040;
}

.stat-value {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.network-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-settings label {
  font-size: 12px;
  color: #969696;
}

.network-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.network-info {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.network-info p {
  margin: 4px 0;
  font-size: 14px;
}

.wallet-connected {
  color: #4ade80;
  font-size: 12px;
}

.terminal-header {
  background: #2d2d2d;
  padding: 8px 16px;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  color: #969696;
}

.terminal-output {
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.terminal-line {
  margin-bottom: 4px;
  color: #d4d4d4;
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: #0e639c;
  font-size: 20px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #969696;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: #3c3c3c;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px 8px 36px;
  color: #d4d4d4;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #0e639c;
}

.search-input::placeholder {
  color: #969696;
}

.header-right {
  display: flex;
  align-items: center;
}

.sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.sign-in-btn:hover {
  background: #1177bb;
}

.dashboard-content {
  padding: 24px;
}

.projects-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.new-project-btn {
  background: #16a34a;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.new-project-btn:hover {
  background: #15803d;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.template-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.template-card-content {
  padding: 20px 18px 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.template-icon {
  color: #007acc;
  font-size: 28px;
  background: #232f3e;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
}

.template-version {
  font-size: 11px;
  color: #858585;
  background-color: #232f3e;
  padding: 2px 8px;
  border-radius: 2px;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
}

.template-info p {
  font-size: 13px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #232f3e;
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: #007acc;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.template-network {
  background: #232f3e;
  color: #4da2ff;
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
   font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Statistics Panel */
.refresh-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  color: #969696;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #404040;
}

.stat-card.primary {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.stat-card.critical {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
}

.stat-card.warning {
  border-color: #ea580c;
  background-color: rgba(234, 88, 12, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.recent-activity {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #969696;
  font-family: monospace;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.activity-label {
  font-size: 12px;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
}

.activity-score.good { color: #4ade80; }
.activity-score.medium { color: #fbbf24; }
.activity-score.poor { color: #ef4444; }

.security-trends {
  margin-bottom: 24px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-label {
  flex: 1;
  font-size: 12px;
  color: #969696;
}

.trend-bar {
  flex: 2;
  height: 6px;
  background-color: #404040;
  border-radius: 3px;
  overflow: hidden;
}

.trend-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend-fill.good { background-color: #4ade80; }
.trend-fill.medium { background-color: #fbbf24; }
.trend-fill.poor { background-color: #ef4444; }

.trend-value {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.quick-stats {
  margin-bottom: 16px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.quick-stat-label {
  font-size: 12px;
  color: #969696;
}

.quick-stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.no-stats, .no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #969696;
  gap: 16px;
}

.load-stats-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* Network Panel */
.add-network-btn {
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-network-btn:hover:not(:disabled) {
  background-color: #15803d;
}

.add-network-btn:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.current-network {
  margin-bottom: 24px;
}

.network-card {
  background-color: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-card:hover {
  background-color: #37373d;
  border-color: #0e639c;
}

.network-card.active {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.network-card.current {
  cursor: default;
  border-color: #16a34a;
  background-color: rgba(22, 163, 74, 0.1);
}

.network-info {
  flex: 1;
}

.network-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969696;
}

.chain-id {
  font-family: monospace;
}

.network-type.testnet {
  color: #fbbf24;
}

.network-type.mainnet {
  color: #4ade80;
}

.network-rpc {
  font-size: 11px;
  color: #969696;
  font-family: monospace;
  margin-top: 4px;
  word-break: break-all;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.network-status.online .status-indicator {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

.network-status.offline .status-indicator {
  background-color: #ef4444;
}

.network-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #0e639c;
  border-color: #0e639c;
  color: #ffffff;
}

.delete-btn:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.save-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.cancel-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.network-edit-form, .network-add-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.network-edit-form input, .network-add-form input {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.network-edit-form label, .network-add-form label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #d4d4d4;
}

.edit-actions, .add-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Enhanced Terminal */
.terminal-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.terminal-line {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chain-ide-container {
    grid-template-columns: 48px 200px 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-bar-center {
    display: none;
  }
}

/* Global Reset for chainIDE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #d4d4d4;
  background: #1e1e1e;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Additional Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-8 { gap: 8px; }
.gap-16 { gap: 16px; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0e639c;
  outline-offset: 2px;
}

/* Transitions */
button,
.network-card,
.stat-card,
.vulnerability-item {
  transition: all 0.2s ease;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}

/* Simple App Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: #d4d4d4;
}

.welcome-message h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #0e639c;
}

.welcome-message p {
  font-size: 18px;
  margin-bottom: 24px;
  color: #969696;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 24px 0;
  max-width: 600px;
}

.feature-item {
  background: #2d2d2d;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #0e639c;
  font-size: 14px;
}

.get-started {
  font-size: 16px;
  color: #4ade80 !important;
  font-weight: 500;
  margin-top: 24px;
}

.panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.file-tree {
  margin-top: 16px;
}

.folder-item, .file-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .file-item:hover {
  background-color: #3c3c3c;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compiler-settings label {
  font-size: 12px;
  color: #969696;
}

.compiler-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.compile-btn, .connect-btn, .deploy-btn, .audit-btn {
  background: #0e639c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.compile-btn:hover, .connect-btn:hover, .deploy-btn:hover, .audit-btn:hover {
  background: #1177bb;
}

.deploy-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.deploy-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deploy-info p {
  margin: 0;
  font-size: 14px;
}

.audit-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-results {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #404040;
}

.stat-value {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.network-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-settings label {
  font-size: 12px;
  color: #969696;
}

.network-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.network-info {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.network-info p {
  margin: 4px 0;
  font-size: 14px;
}

.wallet-connected {
  color: #4ade80;
  font-size: 12px;
}

.terminal-header {
  background: #2d2d2d;
  padding: 8px 16px;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  color: #969696;
}

.terminal-output {
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.terminal-line {
  margin-bottom: 4px;
  color: #d4d4d4;
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: #0e639c;
  font-size: 20px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #969696;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: #3c3c3c;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px 8px 36px;
  color: #d4d4d4;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #0e639c;
}

.search-input::placeholder {
  color: #969696;
}

.header-right {
  display: flex;
  align-items: center;
}

.sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.sign-in-btn:hover {
  background: #1177bb;
}

.dashboard-content {
  padding: 24px;
}

.projects-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.new-project-btn {
  background: #16a34a;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.new-project-btn:hover {
  background: #15803d;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.template-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.template-card-content {
  padding: 20px 18px 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.template-icon {
  color: #007acc;
  font-size: 28px;
  background: #232f3e;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
}

.template-version {
  font-size: 11px;
  color: #858585;
  background-color: #232f3e;
  padding: 2px 8px;
  border-radius: 2px;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
}

.template-info p {
  font-size: 13px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #232f3e;
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: #007acc;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.template-network {
  background: #232f3e;
  color: #4da2ff;
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Statistics Panel */
.refresh-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  color: #969696;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #404040;
}

.stat-card.primary {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.stat-card.critical {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
}

.stat-card.warning {
  border-color: #ea580c;
  background-color: rgba(234, 88, 12, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.recent-activity {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #969696;
  font-family: monospace;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.activity-label {
  font-size: 12px;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
}

.activity-score.good { color: #4ade80; }
.activity-score.medium { color: #fbbf24; }
.activity-score.poor { color: #ef4444; }

.security-trends {
  margin-bottom: 24px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-label {
  flex: 1;
  font-size: 12px;
  color: #969696;
}

.trend-bar {
  flex: 2;
  height: 6px;
  background-color: #404040;
  border-radius: 3px;
  overflow: hidden;
}

.trend-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend-fill.good { background-color: #4ade80; }
.trend-fill.medium { background-color: #fbbf24; }
.trend-fill.poor { background-color: #ef4444; }

.trend-value {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.quick-stats {
  margin-bottom: 16px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.quick-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.quick-stat-label {
  font-size: 12px;
  color: #969696;
}

.quick-stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.no-stats, .no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #969696;
  gap: 16px;
}

.load-stats-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* Network Panel */
.add-network-btn {
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-network-btn:hover:not(:disabled) {
  background-color: #15803d;
}

.add-network-btn:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.current-network {
  margin-bottom: 24px;
}

.network-card {
  background-color: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-card:hover {
  background-color: #37373d;
  border-color: #0e639c;
}

.network-card.active {
  border-color: #0e639c;
  background-color: rgba(14, 99, 156, 0.1);
}

.network-card.current {
  cursor: default;
  border-color: #16a34a;
  background-color: rgba(22, 163, 74, 0.1);
}

.network-info {
  flex: 1;
}

.network-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969696;
}

.chain-id {
  font-family: monospace;
}

.network-type.testnet {
  color: #fbbf24;
}

.network-type.mainnet {
  color: #4ade80;
}

.network-rpc {
  font-size: 11px;
  color: #969696;
  font-family: monospace;
  margin-top: 4px;
  word-break: break-all;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #404040;
}

.network-status.online .status-indicator {
  background-color: #4ade80;
  box-shadow: 0 0 4px #4ade80;
}

.network-status.offline .status-indicator {
  background-color: #ef4444;
}

.network-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  background: none;
  border: 1px solid #404040;
  color: #969696;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #0e639c;
  border-color: #0e639c;
  color: #ffffff;
}

.delete-btn:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.save-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
  color: #ffffff;
}

.cancel-btn:hover {
  background-color: #404040;
  color: #ffffff;
}

.network-edit-form, .network-add-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.network-edit-form input, .network-add-form input {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.network-edit-form label, .network-add-form label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #d4d4d4;
}

.edit-actions, .add-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Enhanced Terminal */
.terminal-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.terminal-line {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chain-ide-container {
    grid-template-columns: 48px 200px 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .top-bar-center {
    display: none;
  }
}

/* Global Reset for chainIDE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #d4d4d4;
  background: #1e1e1e;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Additional Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-8 { gap: 8px; }
.gap-16 { gap: 16px; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a4a;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0e639c;
  outline-offset: 2px;
}

/* Transitions */
button,
.network-card,
.stat-card,
.vulnerability-item {
  transition: all 0.2s ease;
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}

/* Simple App Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: #d4d4d4;
}

.welcome-message h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: #0e639c;
}

.welcome-message p {
  font-size: 18px;
  margin-bottom: 24px;
  color: #969696;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 24px 0;
  max-width: 600px;
}

.feature-item {
  background: #2d2d2d;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #0e639c;
  font-size: 14px;
}

.get-started {
  font-size: 16px;
  color: #4ade80 !important;
  font-weight: 500;
  margin-top: 24px;
}

.panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content h3 {
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.file-tree {
  margin-top: 16px;
}

.folder-item, .file-item {
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover, .file-item:hover {
  background-color: #3c3c3c;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compiler-settings label {
  font-size: 12px;
  color: #969696;
}

.compiler-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.compile-btn, .connect-btn, .deploy-btn, .audit-btn {
  background: #0e639c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.compile-btn:hover, .connect-btn:hover, .deploy-btn:hover, .audit-btn:hover {
  background: #1177bb;
}

.deploy-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.deploy-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deploy-info p {
  margin: 0;
  font-size: 14px;
}

.audit-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-results {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: #2d2d2d;
  padding: 16px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #404040;
}

.stat-value {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: #969696;
  text-transform: uppercase;
}

.network-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-settings label {
  font-size: 12px;
  color: #969696;
}

.network-settings select {
  background: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
}

.network-info {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.network-info p {
  margin: 4px 0;
  font-size: 14px;
}

.wallet-connected {
  color: #4ade80;
  font-size: 12px;
}

.terminal-header {
  background: #2d2d2d;
  padding: 8px 16px;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  color: #969696;
}

.terminal-output {
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.terminal-line {
  margin-bottom: 4px;
  color: #d4d4d4;
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #1e1e1e;
  color: #d4d4d4;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-icon {
  color: #0e639c;
  font-size: 20px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #969696;
  font-size: 14px;
}

.search-input {
  width: 100%;
  background: #3c3c3c;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px 8px 36px;
  color: #d4d4d4;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #0e639c;
}

.search-input::placeholder {
  color: #969696;
}

.header-right {
  display: flex;
  align-items: center;
}

.sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.sign-in-btn:hover {
  background: #1177bb;
}

.dashboard-content {
  padding: 24px;
}

.projects-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.new-project-btn {
  background: #16a34a;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.new-project-btn:hover {
  background: #15803d;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.template-card {
  background: linear-gradient(135deg, #232526 0%, #2c2c2e 100%);
  border: 1.5px solid #232526;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, transform 0.15s;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-card:hover {
  border-color: #007acc;
  box-shadow: 0 4px 16px 0 rgba(0,122,204,0.10);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, #232526 0%, #232f3e 100%);
}

.template-card-content {
  padding: 20px 18px 16px 18px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.template-icon {
  color: #007acc;
  font-size: 28px;
  background: #232f3e;
  border-radius: 6px;
  padding: 6px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
}

.template-version {
  font-size: 11px;
  color: #858585;
  background-color: #232f3e;
  padding: 2px 8px;
  border-radius: 2px;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 6px 0;
}

.template-info p {
  font-size: 13px;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #232f3e;
}

.template-category,
.template-network {
  font-size: 11px;
  color: #ffffff;
  background: #007acc;
  padding: 2px 8px;
  border-radius: 2px;
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.template-network {
  background: #232f3e;
  color: #4da2ff;
}

.import-card {
  border: 2px dashed #464647;
  background: transparent;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-card:hover {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.08);
}

.import-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.upload-arrow {
  font-size: 28px;
  color: #007acc;
}

/* Compiler Panel */
.compiler-panel {
  padding: 16px;
}

.compiler-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  color: #969696;
}

.setting-group select {
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 4px 8px;
  border-radius: 4px;
}

.compile-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.compile-button:hover {
  background-color: #1177bb;
}

/* Audit Panel */
.audit-panel {
  padding: 16px;
}

.audit-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.audit-button {
  width: 100%;
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.audit-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.audit-progress {
  margin-top: 16px;
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 4px;
  background-color: #0e639c;
  transition: width 0.3s ease;
}

.audit-error {
  margin-top: 16px;
  padding: 8px;
  background-color: #5a1d1d;
  color: #ffa7a7;
  border-radius: 4px;
}

/* History Panel */
.history-panel {
  padding: 16px;
}

.history-header {
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 12px;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audit-id {
  font-family: monospace;
  color: #969696;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.status.completed {
  background-color: #1e4620;
  color: #89d185;
}

.status.failed {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.risk-level {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.risk-level.critical {
  background-color: #5a1d1d;
  color: #ffa7a7;
}

.risk-level.high {
  background-color: #6b2c1d;
  color: #ffb997;
}

.risk-level.medium {
  background-color: #6b4c1d;
  color: #ffd397;
}

.risk-level.low {
  background-color: #4c6b1d;
  color: #d3ff97;
}

.empty-history {
  text-align: center;
  color: #969696;
  padding: 32px;
}

/* Terminal */
.terminal {
  padding: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminal-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* File Tree Icons */
.folder-node svg, .file-node svg {
  width: 16px;
  height: 16px;
}

/* Enhanced Panels Styling */
.deploy-panel, .enhanced-audit-panel, .statistics-panel, .network-panel {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.deploy-header, .statistics-header, .network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.deploy-header h3, .statistics-header h3, .network-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 11px;
  text-transform: uppercase;
  color: #969696;
}

.deploy-content, .statistics-content, .network-content {
  flex: 1;
  overflow-y: auto;
}

/* Deployment Panel */
.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.network-name {
  color: #0e639c;
  font-weight: 500;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wallet-status.connected {
  color: #4ade80;
}

.wallet-status.disconnected {
  color: #969696;
}

.connect-wallet-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.connect-wallet-btn:hover {
  background-color: #1177bb;
}

.deploy-button {
  width: 100%;
  background-color: #16a34a;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.deploy-button:hover:not(:disabled) {
  background-color: #15803d;
}

.deploy-button:disabled {
  background-color: #404040;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.deployment-result {
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.deployment-result.success {
  background-color: #1e4620;
  border: 1px solid #16a34a;
}

.deployment-result.error {
  background-color: #5a1d1d;
  border: 1px solid #dc2626;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-item .address, .result-item .hash {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Enhanced Audit Panel */
.audit-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.audit-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.audit-tab.active {
  color: #ffffff;
  border-bottom-color: #0e639c;
}

.audit-by-address {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.address-input-group {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.address-input {
  flex: 1;
  background-color: #3c3c3c;
  color: #d4d4d4;
  border: 1px solid #404040;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
}

.audit-address-btn {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.audit-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.results-summary {
  margin-bottom: 16px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 18px;
  font-weight: bold;
}

.score-value.good { color: #4ade80; }
.score-value.medium { color: #fbbf24; }
.score-value.poor { color: #ef4444; }

.vulnerabilities-list {
  margin-top: 16px;
}

.vulnerability-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.vulnerability-item.critical {
  background-color: #5a1d1d;
  border-left-color: #dc2626;
}

.vulnerability-item.high {
  background-color: #6b2c1d;
  border-left-color: #ea580c;
}

.vulnerability-item.medium {
  background-color: #6b4c1d;
  border-left-color: #d97706;
}

.vulnerability-item.low {
  background-color: #4c6b1d;
  border-left-color: #65a30d;
}

.vuln-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 500;
}

.vuln-severity {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.vuln-severity.critical { background-color: #dc2626; }
.vuln-severity.high { background-color: #ea580c; }
.vuln-severity.medium { background-color: #d97706; }
.vuln-severity.low { background-color: #65a30d; }

/* Vulnerability Check Styles */
.vulnerability-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%);
  color: #d4d4d4;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.vulnerability-header {
  background: rgba(45, 45, 48, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #404040;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
}

.vulnerability-header .header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.vulnerability-header .logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.vulnerability-header .logo-icon {
  font-size: 28px;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.vulnerability-header .logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.vulnerability-header .back-to-dashboard {
  background: linear-gradient(135deg, #007acc 0%, #005a9e 100%);
  border: none;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 122, 204, 0.3);
}

.vulnerability-header .back-to-dashboard:hover {
  background: linear-gradient(135deg, #005a9e 0%, #004080 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.4);
}

.vulnerability-header .header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.vulnerability-content {
  padding: 48px 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.audit-section .section-header {
  text-align: center;
  margin-bottom: 48px;
  position: relative;
}

.audit-section .section-header::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #007acc, #00d4aa);
  border-radius: 2px;
}

.audit-section .section-header h2 {
  font-size: 36px;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 16px 0;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  letter-spacing: -0.5px;
}

.audit-section .section-header p {
  font-size: 18px;
  color: #b3b3b3;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.audit-form {
  background: rgba(37, 37, 38, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(64, 64, 64, 0.5);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 48px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.audit-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #007acc, #00d4aa, #007acc);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.form-group {
  margin-bottom: 28px;
  position: relative;
}

.form-group label {
  display: block;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.address-input, .network-select {
  width: 100%;
  background: rgba(60, 60, 60, 0.8);
  border: 2px solid rgba(64, 64, 64, 0.6);
  border-radius: 12px;
  padding: 16px 20px;
  color: #ffffff;
  font-size: 16px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.address-input:focus, .network-select:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 4px rgba(0, 122, 204, 0.15);
  background: rgba(60, 60, 60, 0.95);
  transform: translateY(-1px);
}

.address-input::placeholder {
  color: #888888;
  font-style: italic;
}

.scan-button {
  width: 100%;
  background: #16a34a;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background 0.2s;
}

.scan-button:hover:not(:disabled) {
  background: #15803d;
}

.scan-button:disabled {
  background: #404040;
  cursor: not-allowed;
}

.results-section {
  background: #252526;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 24px;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #404040;
}

.results-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.export-buttons {
  display: flex;
  gap: 8px;
}

.export-btn {
  background: #2d2d30;
  border: 1px solid #404040;
  color: #d4d4d4;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.export-btn:hover {
  background: #404040;
  border-color: #007acc;
  color: #ffffff;
}

.export-btn:active {
  transform: scale(0.95);
}

/* Input Helper Styles */
.input-helper {
  margin-top: 8px;
}

.input-helper small {
  color: #969696;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-address-btn {
  background: none;
  border: 1px solid #007acc;
  color: #007acc;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-address-btn:hover {
  background: #007acc;
  color: #ffffff;
}

.results-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.risk-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.risk-badge.high {
  background: #dc2626;
  color: #ffffff;
}

.risk-badge.medium {
  background: #d97706;
  color: #ffffff;
}

.risk-badge.low {
  background: #16a34a;
  color: #ffffff;
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .results-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

.result-card {
  background: #2d2d30;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 20px;
}

.result-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.vulnerability-item {
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
}

.vulnerability-item.high {
  border-left: 4px solid #dc2626;
}

.vulnerability-item.medium {
  border-left: 4px solid #d97706;
}

.vulnerability-item.low {
  border-left: 4px solid #16a34a;
}

.vuln-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.vuln-title {
  font-weight: 600;
  color: #ffffff;
}

.vuln-description {
  color: #d4d4d4;
  margin: 8px 0;
  line-height: 1.5;
}

.vuln-recommendation {
  color: #969696;
  font-size: 13px;
  margin: 8px 0;
  line-height: 1.4;
}

.vuln-line {
  font-size: 12px;
  color: #007acc;
  font-family: 'Consolas', monospace;
}

.optimization-item, .compliance-item {
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.optimization-item h5 {
  color: #ffffff;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.optimization-item p {
  color: #d4d4d4;
  margin: 0 0 8px 0;
  font-size: 13px;
}

.savings {
  color: #16a34a;
  font-weight: 600;
  font-size: 12px;
}

.compliance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.compliance-item .compliant {
  color: #16a34a;
  font-weight: 600;
}

.compliance-item .not-applicable {
  color: #969696;
}

.scan-info {
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 4px;
  padding: 16px;
  margin-top: 16px;
}

.scan-info p {
  margin: 4px 0;
  font-size: 13px;
  color: #969696;
  font-family: 'Consolas', monospace;
}

/* Vulnerability Check Card in Dashboard */
.project-card.vulnerability-check {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.project-card.vulnerability-check::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card.vulnerability-check:hover::before {
  opacity: 1;
}

.vulnerability-check-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.vulnerability-check-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.vulnerability-check-subtitle {
  font-size: 12px;
  opacity: 0.9;
}

/* Progress Bar Styles */
.progress-container {
  margin-top: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #404040;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #16a34a 0%, #22c55e 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.progress-text {
  font-size: 12px;
  color: #969696;
  text-align: center;
  text-transform: capitalize;
}

/* Error Message Styles */
.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #2d1b1b;
  border: 1px solid #dc2626;
  border-radius: 6px;
  color: #fca5a5;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 16px;
  flex-shrink: 0;
}

/* Enhanced Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #404040;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Results States */
.no-vulnerabilities,
.no-optimizations {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 4px;
  color: #969696;
  font-style: italic;
}

.success-icon {
  color: #16a34a;
  font-size: 16px;
}

.info-icon {
  color: #3b82f6;
  font-size: 16px;
}

/* Enhanced Risk Badge */
.risk-badge.unknown {
  background: #6b7280;
  color: #ffffff;
}

/* Wallet Modal Styles - Based on working test-wallet.html */
.wallet-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10000 !important;
}

.wallet-modal {
  background-color: #2d2d30 !important;
  border-radius: 8px !important;
  padding: 24px !important;
  min-width: 320px !important;
  max-width: 400px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid #404040 !important;
  position: relative !important;
}

.wallet-modal h3 {
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  margin: 0 0 20px 0 !important;
  text-align: center !important;
}

.wallet-options-list {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  margin-bottom: 20px !important;
}

.wallet-option-card {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 16px !important;
  background-color: #3c3c3c !important;
  border: 2px solid transparent !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.wallet-option-card:hover {
  background-color: #464647 !important;
  border-color: #007acc !important;
  transform: translateY(-1px) !important;
}

.wallet-option-card.metamask:hover {
  border-color: #f6851b !important;
}

.wallet-option-card.phantom:hover {
  border-color: #ab9ff2 !important;
}

.wallet-icon {
  font-size: 24px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.wallet-label {
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.wallet-error {
  color: #f87171 !important;
  font-size: 14px !important;
  text-align: center !important;
  margin-bottom: 16px !important;
  padding: 8px !important;
  background-color: rgba(248, 113, 113, 0.1) !important;
  border-radius: 4px !important;
  border: 1px solid rgba(248, 113, 113, 0.3) !important;
}

.close-modal {
  width: 100% !important;
  background-color: #404040 !important;
  color: #ffffff !important;
  border: none !important;
  padding: 12px !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: background-color 0.2s ease !important;
}

.close-modal:hover {
  background-color: #4a4a4a !important;
}

/* Authentication Styles */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-card {
  background: #252526;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid #3e3e42;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.auth-header {
  margin-bottom: 32px;
}

.auth-header .logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.auth-header .logo-icon {
  font-size: 32px;
  color: #007acc;
}

.auth-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.auth-subtitle {
  color: #cccccc;
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

.auth-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.auth-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #2d2d30;
  border-radius: 8px;
  border: 1px solid #3e3e42;
}

.feature-icon {
  font-size: 18px;
  color: #007acc;
  min-width: 18px;
}

.feature-item span {
  color: #cccccc;
  font-size: 14px;
  font-weight: 500;
}

.auth-actions {
  display: flex;
  justify-content: center;
}

.sign-in-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #007acc;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  justify-content: center;
}

.sign-in-button:hover {
  background: #005a9e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
}

.sign-in-button svg {
  font-size: 18px;
}

/* User Section Styles */
.user-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Clerk Component Overrides */
.cl-userButtonBox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cl-userButtonTrigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.cl-userButtonTrigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Auth Status Component */
.auth-status {
  background: #2d2d30;
  border: 1px solid #3e3e42;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.auth-status.loading {
  text-align: center;
  color: #cccccc;
  font-style: italic;
}

.auth-status-header {
  margin-bottom: 12px;
  border-bottom: 1px solid #3e3e42;
  padding-bottom: 8px;
}

.auth-status-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.auth-status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.status-label {
  color: #cccccc;
  font-weight: 500;
  font-size: 14px;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  background: #1e1e1e;
}

.status-value.success {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.1);
}

.status-value.error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
}

/* Dashboard Auth Status */
.dashboard-auth-status {
  padding: 0 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Auth Status Actions */
.status-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #3e3e42;
}

.test-connection-btn {
  background: #007acc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.test-connection-btn:hover:not(:disabled) {
  background: #005a9e;
  transform: translateY(-1px);
}

.test-connection-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Test Results */
.test-results {
  margin-top: 12px;
  padding: 12px;
  background: #1e1e1e;
  border: 1px solid #3e3e42;
  border-radius: 6px;
}

.test-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #3e3e42;
}

.test-results-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.test-results-header small {
  color: #969696;
  font-size: 12px;
}

.test-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.test-label {
  color: #cccccc;
  font-weight: 500;
}

.test-value {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  background: #2d2d30;
}

.test-value.success {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.1);
}

.test-value.error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
}

.test-value.warning {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
}

.test-error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
  padding: 8px;
  border-radius: 4px;
  font-size: 13px;
}

.test-details {
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px solid #3e3e42;
}

.test-details small {
  color: #969696;
  font-size: 12px;
  font-style: italic;
}