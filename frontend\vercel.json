{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "routes": [{"src": "/", "dest": "/landing.html"}, {"src": "/landing", "dest": "/landing.html"}, {"src": "/app", "dest": "/index.html"}, {"src": "/dashboard", "dest": "/index.html"}, {"src": "/assets/(.*)", "dest": "/assets/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}]}