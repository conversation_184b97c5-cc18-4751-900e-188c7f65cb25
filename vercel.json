{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/", "dest": "/landing.html"}, {"src": "/landing", "dest": "/landing.html"}, {"src": "/app", "dest": "/index.html"}, {"src": "/dashboard", "dest": "/index.html"}, {"src": "/assets/(.*)", "dest": "/assets/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"SUPABASE_URL": "@supabase_url", "SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "OPENROUTER_API_KEY": "@openrouter_api_key", "NODE_ENV": "production"}}