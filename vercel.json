{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/", "dest": "/landing.html"}, {"src": "/landing", "dest": "/landing.html"}, {"src": "/app", "dest": "/index.html"}, {"src": "/dashboard", "dest": "/index.html"}, {"src": "/assets/(.*)", "dest": "/assets/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "outputDirectory": "frontend/dist", "buildCommand": "cd frontend && npm run build", "installCommand": "npm install && cd frontend && npm install", "framework": "vite", "functions": {"api/**/*.js": {"runtime": "nodejs18.x"}}}